import InfoItem from '@/components/Dimension'
import {usePluginExecutor} from '@/hooks/usePluginExecutor'
import type {CodeBlock, SerializeOptions} from '@/lib/codegen'
import type {Plugin} from '@/lib/pluginExecutor'
import pluginStore from '@/store/pluginStore'
import type React from 'react'
import {useEffect, useMemo, useState} from 'react'
import CodeBlockComponent from './CodeBlock'

interface CodeSectionProps {
  style: Record<string, string>
  options: SerializeOptions
  className?: string
}

export const CodeSection: React.FC<CodeSectionProps> = ({style, options, className = ''}) => {
  const {activePlugin} = usePluginExecutor()
  const {activePluginSource, plugins} = pluginStore.state
  const [refreshKey, setRefreshKey] = useState(0)

  // Force refresh when plugin state changes
  useEffect(() => {
    console.log('CodeSection - plugin state changed, forcing refresh')
    setRefresh<PERSON>ey(prev => prev + 1)
  }, [activePluginSource, plugins])

  const {codeBlocks, pluginName} = useMemo(() => {
    /* console.log('CodeSection useMemo triggered:', {
      hasStyle: !!style && Object.keys(style).length > 0,
      activePluginSource,
      pluginsCount: Object.keys(plugins).length,
      activePlugin: !!activePlugin,
      activePluginName: activePlugin?.name || 'none',
    }) */

    if (!style || Object.keys(style).length === 0) {
      return {codeBlocks: [], pluginName: undefined}
    }

    // Pass the activePlugin directly to avoid timing issues
    const result = codegenSync(style, options, activePlugin)
    return result
  }, [style, options, activePlugin, activePluginSource, plugins, refreshKey])

  if (codeBlocks.length === 0) {
    return null
  }

  // console.log('CodeSection - codeBlocks:', codeBlocks)

  return (
    <div className={`${className} w-full`}>
      {codeBlocks.map(block => (
        <InfoItem key={block.name} infoName={block.title} fold={block.title === 'JavaScript' ? true : false}>
          <CodeBlockComponent title={block.title} code={block.code} lang={block.lang} />
        </InfoItem>
      ))}
    </div>
  )
}

// Synchronous version of codegen for React component
function codegenSync(
  style: Record<string, string>,
  options: SerializeOptions,
  activePlugin: Plugin | null = null,
): {codeBlocks: CodeBlock[]; pluginName?: string} {
  const codeBlocks: CodeBlock[] = []
  let plugin = null

  try {
    // Use the passed activePlugin directly to avoid timing issues
    console.log('codegenSync - activePlugin:', activePlugin?.name || 'none')
    if (activePlugin) {
      plugin = activePlugin
    }
  } catch (e) {
    console.error('Failed to get active plugin:', e)
  }

  const {css: cssOptions, js: jsOptions, ...rest} = plugin?.code ?? {}

  // Generate CSS code block
  if (cssOptions !== false) {
    const cssCode = serializeCSS(style, options, cssOptions)
    if (cssCode) {
      codeBlocks.push({
        name: 'css',
        title: cssOptions?.title ?? 'CSS',
        lang: cssOptions?.lang ?? 'css',
        code: cssCode,
      })
    }
  }

  // Generate JS code block
  if (jsOptions !== false) {
    const jsCode = serializeJavaScript(style, options, jsOptions)
    if (jsCode) {
      codeBlocks.push({
        name: 'js',
        title: jsOptions?.title ?? 'JavaScript',
        lang: jsOptions?.lang ?? 'js',
        code: jsCode,
      })
    }
  }

  // Generate additional code blocks from plugin
  if (rest && typeof rest === 'object') {
    codeBlocks.push(
      ...Object.keys(rest)
        .map(name => {
          const extraOptions = (rest as any)[name]
          if (extraOptions === false) {
            return null
          }

          const code = serializeCSS(style, options, extraOptions)
          if (!code) {
            return null
          }
          return {
            name,
            title: extraOptions.title ?? name,
            lang: extraOptions.lang ?? 'css',
            code,
          }
        })
        .filter((item): item is CodeBlock => item != null),
    )
  }

  return {
    codeBlocks,
    pluginName: plugin?.name,
  }
}

// Serialize CSS to string
function serializeCSS(style: Record<string, string>, options: SerializeOptions, transformOptions?: any): string {
  const cssString = Object.entries(style)
    .map(([key, value]) => `${key}: ${value};`)
    .join('\n')

  if (transformOptions?.transform) {
    try {
      return transformOptions.transform({code: cssString, style, options})
    } catch (error) {
      console.error('Failed to transform CSS:', error)
      return cssString
    }
  }

  return cssString
}

// Serialize CSS to JavaScript object with camelCase keys
function serializeJavaScript(style: Record<string, string>, options: SerializeOptions, transformOptions?: any): string {
  // Convert kebab-case to camelCase
  const toCamelCase = (str: string): string => {
    return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
  }

  // Create JS object with camelCase keys
  const jsObject = Object.entries(style).reduce(
    (obj, [key, value]) => {
      obj[toCamelCase(key)] = value
      return obj
    },
    {} as Record<string, string>,
  )

  // Convert to formatted string
  const jsString = `{\n${Object.entries(jsObject)
    .map(([key, value]) => `  ${key}: "${value}"`)
    .join(',\n')}\n}`

  if (transformOptions?.transform) {
    try {
      return transformOptions.transform({code: jsString, style: jsObject, options})
    } catch (error) {
      console.error('Failed to transform JavaScript:', error)
      return jsString
    }
  }

  return jsString
}

export default CodeSection
