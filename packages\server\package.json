{"name": "@i2c/server", "module": "index.ts", "type": "module", "devDependencies": {"@figma/rest-api-spec": "0.33.0", "@types/bun": "latest", "cross-env": "^7.0.3"}, "scripts": {"dev": "bun --watch run src/index.ts", "dev:https": "cross-env ENABLE_HTTPS=true bun --watch run src/index.ts", "start": "bun run src/index.ts", "lint": "biome check . --fix", "build": "bun build --target=node ./src/index.ts --outdir ./dist", "compile:linux": "bun build --compile --target=bun-linux-x64 ./src/index.ts --outfile ./dist/app", "compile": "bun build --compile --minify --sourcemap ./src/index.ts --outfile ./dist/app", "serve": "./dist/app", "build:prod": "bun build src/index.ts --outdir dist --target bun --minify --bundle --sourcemap"}, "dependencies": {"@baidu/f2c-dsl-adapter": "workspace:*", "lodash": "^4.17.21", "marked": "^15.0.12", "tinify": "^1.8.1"}}