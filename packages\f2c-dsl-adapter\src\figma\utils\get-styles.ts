import type {Node, TextNode} from '@figma/rest-api-spec'
import {
  getBorderColor,
  getBorderRadius,
  getBorderStyle,
  getBorderWidth,
  getFillsColor,
  getTextColor,
  getTextStyles,
} from '../css-util' //

/**
 * Retrieves the background style of a Figma node.
 * @param node The Figma node.
 * @returns The background CSS string or undefined.
 */
export function getBackground(node: Node): string | undefined {
  const attributes: Record<string, string> = {}
  return getFillsColor(node, attributes) //
}

/**
 * Retrieves the text color style of a Figma node.
 * @param node The Figma node.
 * @returns The text color CSS string or undefined.
 */
export function getColor(node: Node): string | undefined {
  const attributes: Record<string, string> = {}
  return getTextColor(node, attributes) //
}

/**
 * Retrieves the font family style of a text node.
 * @param node The Figma node.
 * @returns The font family CSS string or undefined.
 */
export function getFontFamily(node: Node): string | undefined {
  if (node.type !== 'TEXT') return undefined //
  return getTextStyles(node).fontFamily //
}

/**
 * Retrieves the font size style of a text node.
 * @param node The Figma node.
 * @returns The font size CSS string or undefined.
 */
export function getFontSize(node: Node): string | undefined {
  if (node.type !== 'TEXT') return undefined //
  return getTextStyles(node).fontSize //
}

/**
 * Retrieves the font weight style of a text node.
 * @param node The Figma node.
 * @returns The font weight CSS string or undefined.
 */
export function getFontWeight(node: Node): string | undefined {
  if (node.type !== 'TEXT') return undefined //
  return getTextStyles(node).fontWeight //
}

/**
 * Retrieves the line height style of a text node.
 * @param node The Figma node.
 * @returns The line height CSS string or undefined.
 */
export function getLineHeight(node: Node): string | undefined {
  if (node.type !== 'TEXT') return undefined //
  return getTextStyles(node).lineHeight //
}

// Re-export border-related functions for direct use in getStyles
export {getBorderRadius, getBorderColor, getBorderStyle, getBorderWidth} from '../css-util' //
