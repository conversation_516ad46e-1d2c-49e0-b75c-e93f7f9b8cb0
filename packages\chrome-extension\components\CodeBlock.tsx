import CodeCom from '@/entrypoints/popup/Dimension/code'
import Prism from 'prismjs'
import type React from 'react'
import {useMemo} from 'react'

interface CodeBlockProps {
  title: string
  code: string
  lang: string
  className?: string
}

export const CodeBlock: React.FC<CodeBlockProps> = ({title, code, lang, className = ''}) => {
  const highlightedCode = useMemo(() => {
    if (code) {
      // Use the appropriate language for syntax highlighting
      const language = Prism.languages[lang] || Prism.languages.css
      return Prism.highlight(code, language, lang)
    }
    return ''
  }, [code, lang])

  if (!code) {
    return null
  }

  return (
    <div className={`mb-2 ${className}`}>
      <CodeCom highlightedCode={highlightedCode} copyResult={code} />
    </div>
  )
}

export default CodeBlock
