import {Button} from '@/components/ui/button'
import {HoverCard, HoverCardContent, HoverCardTrigger} from '@/components/ui/hover-card'
import cutDiagramStore from '@/store/cutDiagramStore'
import nodeStore from '@/store/nodeStore'
import {useEffect, useState} from 'react'
import {toast} from 'sonner'

// 获取节点尺寸的辅助函数
function getNodeDimensions(node: SceneNode): {width: number; height: number} {
  // 优先使用 absoluteRenderBounds（需要类型检查）
  try {
    if ('absoluteRenderBounds' in node && node.absoluteRenderBounds) {
      return {
        width: Math.ceil(node.absoluteRenderBounds.width),
        height: Math.ceil(node.absoluteRenderBounds.height),
      }
    }

    // 其次使用 absoluteBoundingBox
    if (node.absoluteBoundingBox) {
      return {
        width: Math.ceil(node.absoluteBoundingBox.width),
        height: Math.ceil(node.absoluteBoundingBox.height),
      }
    }

    // 最后使用基本的 width 和 height
    return {
      width: node.width,
      height: node.height,
    }
  } catch (error) {
    return {
      width: node.width,
      height: node.height,
    }
  }
}

export default function Preview() {
  const currentSelections = nodeStore.state.currentSelections
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const {cutDiagrams, setCutDiagrams} = cutDiagramStore.state

  useEffect(() => {
    if (currentSelections.length === 1) {
      const node = currentSelections[0]
      setLoading(true)
      setPreviewImage(null)
      generatePreview(node)
    } else {
      setPreviewImage(null)
      setLoading(false)
    }
  }, [currentSelections])

  const generatePreview = async (node: SceneNode) => {
    try {
      const {width, height} = node
      const exportSettings: ExportSettings = {format: 'PNG',constraint: {
        type: 'SCALE',
        value: (width > 1000 || height > 1000) ? 0.5 : 1,
      } }
      const imageData = await node.exportAsync(exportSettings)
      const base64Image = window.figma.base64Encode(imageData)
      setPreviewImage(`data:image/png;base64,${base64Image}`)
    } catch (e) {
      console.error('Error generating preview:', e)
      setPreviewImage(null)
    } finally {
      setLoading(false)
    }
  }

  const handleAddCutDiagram = () => {
    if (currentSelections.length === 0) {
      toast.error('请先选择一个图层')
      return
    }

    const newDiagrams = currentSelections
      .map(node => {
        const dimensions = getNodeDimensions(node)
        return {
          id: node.id,
          name: node.name,
          format: 'png',
          scale: 1,
          width: dimensions.width,
          height: dimensions.height,
          isSelected: true,
          key: `${node.id}1png`,
        }
      })
      .filter(newDiagram => !cutDiagrams.find(d => d.key === newDiagram.key))

    if (newDiagrams.length > 0) {
      setCutDiagrams([...cutDiagrams, ...newDiagrams])
      toast.success(`已成功添加 ${newDiagrams.length} 个图层`)
    } else {
      toast.info('所选图层均已在列表中')
    }
  }

  return (
    <div className="p-2 bg-gray-50 rounded shadow-inner text-xs">
      <p className="font-bold">预览</p>
      <div className="h-20 flex items-center justify-center flex-wrap">
        {loading ? (
          <div className="text-center text-gray-500">正在生成预览...</div>
        ) : currentSelections.length > 1 ? (
          <div className="text-center text-gray-500">{`已选择 ${currentSelections.length} 个图层`}</div>
        ) : previewImage ? (
          <HoverCard>
            <HoverCardTrigger asChild>
              <img
                src={previewImage}
                alt="Preview"
                className="object-contain max-w-full max-h-full rounded cursor-pointer"
              />
            </HoverCardTrigger>
            <HoverCardContent className="w-120 flex items-center justify-center flex-wrap max-h-100 bg-gray-50">
              <img src={previewImage} alt="Full Preview" className="min-w-60 max-w-120 max-h-80 object-contain" />
            </HoverCardContent>
          </HoverCard>
        ) : (
          <div className="text-center text-gray-500">请选择一个图层以预览</div>
        )}
      </div>
      <Button
        onClick={handleAddCutDiagram}
        size="sm"
        className="text-xs w-full cursor-pointer mt-2 bg-white text-gray-800 hover:bg-gray-50 shadow-md hover:shadow-lg transition-shadow"
        disabled={currentSelections.length === 0}
      >
        {currentSelections.length > 1 ? `添加 ${currentSelections.length} 个图层至切图列表` : '添加至切图列表'}
      </Button>
    </div>
  )
}
