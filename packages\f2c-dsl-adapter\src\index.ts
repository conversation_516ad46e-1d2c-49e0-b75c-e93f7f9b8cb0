// import {writeFileSync} from 'node:fs'
import {
  extraFileRegistryGlobalInstance,
  instantiateExtraFileRegistryGlobalInstance,
} from '@baidu/f2c-dsl-react/dist/index'
import type {Option} from '@baidu/f2c-plugin-base/dist/types'
import type {Node} from '@figma/rest-api-spec'
import {generateCodingFiles} from './dsl'
import {initAdapters} from './figma'
import {RestApiAdapter} from './figma/restApiAdapter'
import {SetGeneratorConfig} from './figma/store/coreStore'
import {nameStore} from './figma/store/nameStore'
import Log from './figma/utils/Log'

export const defaultOption = {
  language: 'typescript',
  uiFramework: 'react',
  cssFramework: 'inlinecss',
  cmpName: 'Rectangle 5184',
  outputDirName: 'src/f2c/views/',
  outPutImageDirName: 'res/layout/',
  designWidth: 375,
  fileOutPutMode: 'vscode',
  outputImgScale: '1',
  screenAdaptType: 'stretch',
  absoluteLayout: false,
  scaleSize: 1,
  isCompressImage: false,
  tinypngApiKey: '',
  isF2CStyles: false,
  AiModel: '',
  isDslMode: true,
  sizeAdaption: 1,
  analyzeShadow: true,
  analyzeLinearGradient: true,
  htmp: false,
  busiSetting: {},
  aiPreParse: false,
  isInternal: true,
  componentName: 'Rectangle 5184',
  cssUnit: 'px',
  useInlineStyle: false,
  useDupStyleOpt: false,
  useInheritableOpt: false,
  useShorthand: false,
  useHexColor: false,
  useImgAlt: false,
  imgFormat: 'png',
  // jsonMode 不考虑在插件环境下生成完清理数据的问题，rest-api 下启用
  jsonMode: true,
  skipGroupingNode: false,
  reserverFigmaMask: true,
  reserveFont: true,
  // preserveRendering: true,
} as Option
export const convertToCode = async (figmaNodes: Node[], userOption = defaultOption) => {
  const {id, name} = figmaNodes[0]
  if (figmaNodes.length < 1) {
    return {
      id: '',
      name: '',
      files: [],
    }
  }
  const startTime = Date.now()
  nameStore.reset()
  instantiateExtraFileRegistryGlobalInstance()
  const option = {...defaultOption, ...userOption}
  Log.debug(`convertToCode option:`, option)
  if (option.htmp) {
    option.skipGroupingNode = true
  }

  SetGeneratorConfig(option, figmaNodes[0])

  const {analyticalNode} = initAdapters(figmaNodes, option)

  const files = await generateCodingFiles(analyticalNode, option)

  const endTime = Date.now()
  const elapsedTime = endTime - startTime // 计算耗时

  Log.debug(`Total Execution time: ${elapsedTime} ms`)
  const importStatements = extraFileRegistryGlobalInstance.getImportComponentMeta().map(item => ({
    importPath: item.importPath,
    id: item.node.node.id,
    name: item.node.name,
    nodeType: item.node.originType,
  }))
  // console.log('importStatements', importStatements)
  return {name, id, files, importStatements}
}
export {initAdapters, generateCodingFiles, RestApiAdapter}
