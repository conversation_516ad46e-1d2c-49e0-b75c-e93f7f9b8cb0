import {Button} from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/radix-select'
import {extensionMessageService} from '@/lib/extension-message-service'
import dimensionStore, {Platform} from '@/store/dimensionStore'
import {Download, RulerDimensionLine, SquareMousePointer} from 'lucide-react'
import {useEffect, useState} from 'react'

import panelStore from '@/store/panelStore'
// import loginStore from '@/store/loginStore'
// import businessStore from '@/store/uploadStore'

interface UpdateInfo {
  current: string
  latest: string
  hasUpdate: boolean
}

export function Menu() {
  const [version, setVersion] = useState<string>('...')
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)
  const {lockDeepSelection, lockLockAltKey, setLockDeepSelection, setLockAltKey} = panelStore.state

  useEffect(() => {
    const loadVersion = async () => {
      try {
        const currentVersion = await extensionMessageService.getCurrentVersion()
        setVersion(currentVersion)
      } catch (error) {
        console.warn('获取版本失败:', error)
        setVersion('未知')
      }
    }

    loadVersion()
  }, [])

  // 监听更新通知
  useEffect(() => {
    const updateListener = async (details: any) => {
      console.log('DropdownMenu 收到更新通知:', details)
      setUpdateInfo({
        current: version,
        latest: details.version,
        hasUpdate: true,
      })
    }

    extensionMessageService.addUpdateListener(updateListener)
    return () => {
      extensionMessageService.removeUpdateListener(updateListener)
    }
  }, [version])

  // 处理更新
  const handleUpdate = async () => {
    setIsUpdating(true)
    try {
      await extensionMessageService.applyUpdate()
    } catch (error) {
      console.error('更新失败:', error)
      setIsUpdating(false)
    }
  }
  // const { businessList, selectedBusiness, setSelectedBusiness ,seletedBucket, setSeletedBucket} = businessStore.state
  // const { isLoggedIn } = loginStore.state
  const {
    currentPlatform,
    setCurrentPlatform,
    currentSelectUnit,
    currentMagnification,
    currentVwBase,
    currentRemBase,
    setCurrentSelectUnit,
    setCurrentRemBase,
    setCurrentMagnification,
    setCurrentVwBase,
  } = dimensionStore.state

  // 根据平台获取可用单位
  const getUnitsByPlatform = (platform: Platform) => {
    switch (platform) {
      case Platform.WEB:
        return ['px', 'rem', 'vw']
      case Platform.ANDROID:
        return ['dp']
      case Platform.IOS:
        return ['px']
      default:
        return ['px', 'rem', 'vw']
    }
  }

  const renderExtraMenuItemByUnit = (unit: string) => {
    switch (unit) {
      case 'vw':
        return (
          <DropdownMenuItem className="text-xs">
            <span className="w-40 text-xs">viewportWidth</span>
            <input
              type="text"
              className="w-16 h-8 px-2 rounded text-xs"
              value={currentVwBase}
              onChange={e => setCurrentVwBase(e.target.value)}
              onClick={e => e.stopPropagation()}
            />
          </DropdownMenuItem>
        )
      case 'rem':
        return (
          <DropdownMenuItem className="text-xs">
            <span className="w-40 text-xs">Rem基准</span>
            <input
              type="text"
              className="w-16 h-8 px-2 rounded text-xs"
              value={currentRemBase}
              onChange={e => setCurrentRemBase(e.target.value)}
              onClick={e => e.stopPropagation()}
            />
          </DropdownMenuItem>
        )
      default:
        return null
    }
  }

  // 平台选择框变更
  const handlePlatformChange = (platform: Platform) => {
    setCurrentPlatform(platform)
    // 默认选择第一个单位
    setCurrentSelectUnit(getUnitsByPlatform(platform)[0])
  }

  // useEffect(() => {
  //   if (isLoggedIn && businessList?.length === 0) {
  //     businessStore.getBuisinessList()
  //   }
  // }, [isLoggedIn, businessList])

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger className="text-gray-500 hover:text-gray-700 cursor-pointer relative text-xs" asChild>
        <div className="relative">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z" />
            <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z" />
          </svg>
          {/* 红点提示 */}
          {updateInfo?.hasUpdate && <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-50 text-xs">
        <DropdownMenuLabel className="flex items-center text-xs">
          设置 <div className="ml-2 text-xs text-gray-500">v{version}</div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-muted" />

        {/* 更新提示 */}
        {updateInfo?.hasUpdate && (
          <>
            <DropdownMenuItem className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-300 rounded-md mx-2 my-1 shadow-sm text-xs">
              <div className="flex items-center justify-between w-full">
                <div className="flex-1">
                  <div className="text-xs font-semibold text-blue-800 flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                    发现新版本
                  </div>
                  <div className="text-xs text-blue-700 font-medium">
                    v{updateInfo.current} → v{updateInfo.latest}
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleUpdate}
                  disabled={isUpdating}
                  className={`ml-3 h-8 w-8 p-0 ${isUpdating ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                  title={isUpdating ? '更新中...' : '立即更新'}
                >
                  <Download className="w-4 h-4 text-blue-800" />
                </Button>
              </div>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-muted" />
          </>
        )}
        <DropdownMenuItem
          // onSelect={e => e.preventDefault()}
          className="hover:bg-transparent focus:bg-transparent data-[highlighted]:bg-transparent text-xs"
        >
          <span className="w-40 text-xs">工具</span>
          <div className="flex items-center gap-3">
            <Button
              size="sm"
              variant="ghost"
              className={`cursor-pointer p-1 h-8 w-8 ${lockDeepSelection ? 'bg-blue-100 hover:bg-blue-100' : 'hover:bg-gray-100'}`}
              onClick={e => {
                e.stopPropagation()
                setLockDeepSelection(!lockDeepSelection)
              }}
            >
              <SquareMousePointer className={`h-4 w-4 ${lockDeepSelection ? 'text-blue-600' : 'text-gray-600'}`} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`cursor-pointer p-1 h-8 w-8 ${lockLockAltKey ? 'bg-blue-100 hover:bg-blue-100' : 'hover:bg-gray-100'}`}
              onClick={e => {
                e.stopPropagation()
                setLockAltKey(!lockLockAltKey)
              }}
            >
              <RulerDimensionLine className={`h-4 w-4 ${lockLockAltKey ? 'text-blue-600' : 'text-gray-600'}`} />
            </Button>
          </div>
        </DropdownMenuItem>
        <DropdownMenuGroup>
          <DropdownMenuItem className="text-xs">
            <span className="w-40 text-xs">平台选择</span>
            <Select value={currentPlatform} onValueChange={handlePlatformChange}>
              <SelectTrigger className="border-0 shadow-none text-xs">
                <SelectValue placeholder="" />
              </SelectTrigger>
              <SelectContent className="text-xs">
                {Object.values(Platform).map(platform => (
                  <SelectItem key={platform} value={platform} className="text-xs">
                    {platform}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </DropdownMenuItem>

          <DropdownMenuItem className="text-xs">
            <span className="w-40 text-xs">单位</span>
            <Select value={currentSelectUnit} onValueChange={setCurrentSelectUnit}>
              <SelectTrigger className="border-0 shadow-none text-xs">
                <SelectValue placeholder="" />
              </SelectTrigger>
              <SelectContent className="text-xs">
                {getUnitsByPlatform(currentPlatform).map(item => (
                  <SelectItem key={item} value={item} className="text-xs">
                    {item}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </DropdownMenuItem>

          {/* 根据选择的单位显示不同额外设置项 */}
          {renderExtraMenuItemByUnit(currentSelectUnit)}

          <DropdownMenuItem className="text-xs">
            <span className="w-40 text-xs">倍率</span>
            <Select value={String(currentMagnification)} onValueChange={setCurrentMagnification}>
              <SelectTrigger className="border-0 shadow-none text-xs">
                <SelectValue placeholder="1" />
              </SelectTrigger>
              <SelectContent className="text-xs">
                {['0.5', '1', '2', '3'].map(item => (
                  <SelectItem key={item} value={item} className="text-xs">
                    {item}x
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </DropdownMenuItem>
          {/* {businessList.length > 0 && <DropdownMenuItem>
            <span className="w-40">业务选择</span>
            <Select value={String(selectedBusiness?.id) || ''} onValueChange={setSelectedBusiness}>
              <SelectTrigger className="border-0 shadow-none">
                <SelectValue placeholder="1" />
              </SelectTrigger>
              <SelectContent>
                {businessList.length > 0 && businessList.map(item => (
                  <SelectItem key={item.id} value={String(item.id)}>
                    {item.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </DropdownMenuItem>}
          {selectedBusiness?.buckets && <DropdownMenuItem>
            <span className="w-40">Bucket</span>
            <Select value={String(seletedBucket?.id)} onValueChange={setSeletedBucket}>
              <SelectTrigger className="border-0 shadow-none">
                <SelectValue placeholder="1" />
              </SelectTrigger>
              <SelectContent>
                {selectedBusiness?.buckets.map(item => (
                  <SelectItem key={item.id} value={String(item.id)}>
                    {item.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </DropdownMenuItem>} */}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
