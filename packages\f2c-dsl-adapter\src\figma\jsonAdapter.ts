import type {UiFramework} from '@baidu/f2c-plugin-base/dist/types/constants'
import type {Node} from '@figma/rest-api-spec'
import {filterAttributes} from './f2c/util'
import {
  type Annotations,
  type Attributes,
  type BoxCoordinates,
  type FilterOption,
  NodeType,
  type PostionalRelationship,
  type PropertyVariable,
  type StyledTextSegment,
} from './type'
import {isEmpty} from './utils/utils'
export class JsonAdapter {
  id = ''
  name = ''
  // 原 order
  order = ''
  // 分组后的 order
  pregeneratingOrder = ''
  type?: NodeType
  children: JsonAdapter[] = []
  node?: Node
  annotations: Annotations = {}
  cssAttributes: Attributes = {}
  positionalCssAttributes: Attributes = {}
  htmlAttributes: Attributes = {}

  // 这两个属性为了计算相对坐标，先废弃
  absRenderingBox?: BoxCoordinates
  rootAbsRenderingBox?: BoxCoordinates
  //
  uiFrameWork?: UiFramework
  absoluteLayout?: boolean
  inlineStyle = ''
  reactions = []
  reactionsState = null

  boundVariables: {[propertyKey: string]: PropertyVariable} = {}
  // 文字图层
  textInfo: {
    text?: string
    paragraphSpacing?: number
    styledTextSegments?: StyledTextSegment[]
    listSpacing?: number
  } = {}

  stylesClassName = ''
  fills = []
  isPerfectCircle = false

  aiRenamed = ''

  // constructor(data?: any) {
  //   if (data) {
  //     this.initDataFromJson(data)
  //   }
  // }
  // initDataFromJson(data) {
  //   for (const key in data) {
  //     if (data.hasOwnProperty(key)) {
  //       this[key] = data[key]
  //     }
  //   }
  // }
  constructor(node: Node | null, children: JsonAdapter[] = [], type = NodeType.VISIBLE) {
    this.node = node
    this.children = children
    this.type = type
  }
  getOriginalId() {
    if (this.node) {
      return this.node.id
    }
    return ''
  }
  getOriginalType() {
    if (this.node) {
      return this.node.type
    }
    return ''
  }

  getIsPerfectCircle() {
    return this.isPerfectCircle
  }

  getCssAttributes(): Attributes {
    return this.cssAttributes
  }
  getACssAttribute(key: string): any {
    return this.cssAttributes[key]
  }

  getPositionalCssAttributes(): Attributes {
    return this.positionalCssAttributes
  }

  getStylesClassName() {
    return this.stylesClassName
  }

  getId() {
    return this.id
  }

  getStyledTextSegments() {
    return this.textInfo.styledTextSegments
  }

  getParagraphSpacing(): number {
    return this.textInfo.paragraphSpacing!
  }

  getText() {
    return this.textInfo.text
  }

  getType(): string {
    return this.type!
  }

  getFills() {
    return this.fills
  }

  getAnnotation(key: string): any {
    return this.annotations[key]
  }

  hasAnnotation(key: string): boolean {
    return this.annotations[key] !== undefined
  }

  getReactionsState() {
    return this.reactionsState
  }

  getName() {
    return this.name
  }

  setName(name: string) {
    this.name = name
  }

  public addAnnotations(key: string, value: any) {
    this.annotations[key] = value
  }

  addHtmlAttributes(attrs: Attributes) {
    this.htmlAttributes = {
      ...this.htmlAttributes,
      ...attrs,
    }
  }

  setPositionalCssAttributes(attributes: Attributes, isFilter = true) {
    this.positionalCssAttributes = attributes
    if (isFilter) {
      this.filterCssAttributes()
    }
  }

  setCssAttributes(attributes: Attributes, isFilter = true) {
    this.cssAttributes = attributes
    if (isFilter) {
      this.filterCssAttributes()
    }
  }

  addCssAttributes(attributes: Attributes) {
    this.cssAttributes = {
      ...this.cssAttributes,
      ...attributes,
    }
    this.filterCssAttributes()
  }
  removePositionalCssAttributes(key: string) {
    delete this.positionalCssAttributes[key]
  }
  filterCssAttributes(
    option: FilterOption = {
      zeroValueAllowed: false,
      truncateNumbers: true,
      absolutePositioningFilter: false,
      marginFilter: false,
      imgPropsFilter: false,
    },
  ) {
    this.setCssAttributes(filterAttributes(this.cssAttributes, option), false)
    this.setPositionalCssAttributes(filterAttributes(this.positionalCssAttributes, option), false)
  }

  addPositionalCssAttributes(attributes: Attributes) {
    this.positionalCssAttributes = {
      ...this.positionalCssAttributes,
      ...attributes,
    }
    this.filterCssAttributes()
  }

  getOrder() {
    return this.order
  }

  setOrder(order: string) {
    this.order = order
  }

  getPregeneratingOrder() {
    return this.pregeneratingOrder
  }

  setPregeneratingOrder(order: string) {
    this.pregeneratingOrder = order
  }

  getAPositionalAttribute(key: string): string {
    return this.positionalCssAttributes[key]
  }

  setStylesClassName(stylesClassName: string) {
    this.stylesClassName = stylesClassName
  }

  setChildren(children: JsonAdapter[]) {
    this.children = children
  }
  addChildren(children: JsonAdapter[]) {
    this.children = this.children.concat(children)
  }
  getChildren(): JsonAdapter[] {
    return this.children
  }
  setType(type: NodeType) {}

  initCssAttributes() {}
  getComputedCoordinates(): BoxCoordinates | null {
    return null
  }
  getAbsoluteBoundingBoxCoordinates() {}
  getRenderingBoundsCoordinates() {}
  getComputedContentCoordinates() {}
  computeAbsRenderingBox() {}
  initStyledTextSegments() {}
  handleStyledTextSegments() {}
  initParagraphSpacing() {}
  getFillsByFigmaNode() {}
  initFills() {}

  getPositionalRelationship(targetNode: typeof this): PostionalRelationship | void {}
  // 兼容DSL
  getSharedPluginData() {
    return ''
  }
  async export() {
    return ''
  }

  initText(): string {
    if (!this.node || !('characters' in this.node)) return ''

    if (isEmpty(this.node.characters)) {
      return ''
    }
    this.textInfo.text = this.node.characters
    return this.node.characters
  }
  setWidthAndHeight() {}
  getStyles(props: {
    whType?: 'boundingbox' | 'renderbounds'
    unit?: string
    kebabcase?: boolean
  }) {}
}
