import {BaseStore} from './baseStore'

export type CutDiagramItem = {
  id: string
  content: string
  path: string
  name: string
  width: number
  height: number
  bosUrl?: string
  scale: number
  key: string
  format: 'PNG' | 'SVG' | 'JPG'
  isSelected?: boolean
}

export class CutDiagramStore extends BaseStore {
  constructor() {
    super()
  }
  cutDiagramsRef: CutDiagramItem[] = []

  cutDiagrams: CutDiagramItem[] = []
  exportsCutDiagrams: CutDiagramItem[] = []
  setCutDiagrams(cutDiagrams: CutDiagramItem[]) {
    this.cutDiagrams = cutDiagrams || []
  }
  setExportsCutDiagrams(exportsCutDiagrams: CutDiagramItem[]) {
    this.exportsCutDiagrams = exportsCutDiagrams || []
  }
}
export default new CutDiagramStore()
