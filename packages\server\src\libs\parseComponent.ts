import {RestApiAdapter} from '@baidu/f2c-dsl-adapter'
import type {FrameNode, Node} from '@figma/rest-api-spec'
import merge from 'lodash/merge'
import {figmaAPI} from 'src/services/api'

// 从 component-registry/utils.ts 提取
export const setPathValue = (obj, path, value) => {
  const pathParts = path.split('.')
  let current = obj
  for (let i = 0; i < pathParts.length; i++) {
    const part = pathParts[i]
    if (i === pathParts.length - 1) {
      current[part] = value
    } else {
      current[part] = current[part] || {}
      current = current[part]
    }
  }
  return obj
}

// 从 component-registry/utils.ts 提取
export const decodeHTMLEntities = (text: string) => {
  const entities = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#039;': "'",
  }
  return text.replace(/&[^;]+;/g, function (entity) {
    return entities[entity] || entity
  })
}

// 从 component-registry/index.ts 提取
export const getSubNodePath = (nodePath: string) => {
  if (!nodePath.includes('>')) return nodePath
  const arr = nodePath.split('>')
  arr.shift()
  return arr.join('>') || 'root'
}

// 类型定义，源自 component-registry/types.ts
export type IPropValueFunction = (node: Node, funcs: Record<string, Function>) => any
export type IPropValueObject = {nodePath: string | string[]; getPropValue: IPropValueFunction | string}
export type IPropValueArray = IPropValueObject[]
type IDynamicPropsValue = IPropValueArray | IPropValueFunction | IPropValueObject

export const returnFunTpl = (funStr: string) => {
  return `const res = (${funStr})(node, props, extraProps); return res;`.replace(
    /(\d+)\s*>\s*(\d+)/g,
    function (match, num1, num2) {
      // eslint-disable-next-line prettier/prettier
      return num1 + '&gt;' + num2
    },
  )
}

export class ComponentMarkImpl {
  funStr: string
  node: Node
  pluginDataObj = {} as any
  pathToNodeMap: Record<string, Node[]> = {}
  urlParams: Record<string, string> = {}

  // 模拟 parseFuncMap, 您需要根据您的业务来实现这些方法
  parseFuncMap: Record<string, any> = {
    getNodeText: (node: Node) => {
      // 您的实现: 从 'Text' 类型的 node 中获取 characters
      if ('characters' in node) {
        return (node as any).characters.replaceAll('\n', '')
      }
      return ''
    },
    getNodeImg: async (node: Node) => {
      // 您的实现: 获取图片的路径
      const res = await figmaAPI.getImage(this.urlParams.fileKey, [node.id])
      return res.images[node.id] || ''
    },
    getCssAttributes: (node: Node) => {
      // 您的实现: 获取节点的 CSS 属性
      return this.parseFuncMap.getStyles(node)
    },
    getStyles: (node: Node) => {
      const adapterNode = new RestApiAdapter(node)
      return adapterNode.getStyles()
    },
    getTextColor: (node: Node) => {
      const {color, opacity} = this.parseFuncMap.getStyles(node)
      if (opacity) {
        const fixOpacity = Number(opacity).toFixed(1)
        return `${color.replace(/\d(?=\))/g, fixOpacity)}`
      }
      return color
    },
    getFontSize: (node: Node) => {
      const {fontSize} = this.parseFuncMap.getStyles(node)
      return fontSize
    },
  }

  constructor(funStr: string, node: any, params: Record<string, string> = {}) {
    this.funStr = funStr
    this.node = node
    this.urlParams = params
    const pluginDataFun = new Function('node', 'props', 'extraProps', returnFunTpl(funStr))
    this.pluginDataObj = pluginDataFun(node, {}, {})

    // 初始化 pathToNodeMap
    this.trackNode(this.node)
    this.pathToNodeMap['root'] = [this.node]
  }

  // 从 component-registry/impl/processConfig.ts 提取并简化
  findNodesFromNodePath(key: any): Node[] {
    const nodes: Node[] = []
    if (typeof key === 'string') {
      for (const nodePath in this.pathToNodeMap) {
        if (nodePath.split('>').pop() === key) {
          nodes.push(...this.pathToNodeMap[nodePath])
        }
      }
    } else if (key instanceof RegExp) {
      for (const nodePath in this.pathToNodeMap) {
        if (key.test(nodePath.split('>').pop()) || key.test(nodePath)) {
          nodes.push(...this.pathToNodeMap[nodePath])
        }
      }
    }
    return nodes
  }

  // 从 component-registry/index.ts 提取并适配
  trackNode(node: Node, path = '') {
    const nodePath = path + node.name
    const subNodePath = getSubNodePath(nodePath)

    if (!this.pathToNodeMap[subNodePath]) {
      this.pathToNodeMap[subNodePath] = []
    }
    this.pathToNodeMap[subNodePath].push(node)

    if ('children' in node) {
      for (const child of node.children) {
        if (child) {
          const childPath = path + node.name + '>'
          this.trackNode(child, childPath)
        }
      }
    }
  }

  // 从 component-registry/impl/componentMarkImpl.ts 重构并支持异步
  async parseObjectPropValue(propKey: string, data: IPropValueObject): Promise<any> {
    const props = {}
    let node: FrameNode | undefined
    let nodePaths: string[] = []

    if (Array.isArray(data.nodePath)) {
      nodePaths = data.nodePath
    } else {
      nodePaths = [String(data.nodePath)]
    }

    for (const rawNodePath of nodePaths) {
      const nodePath = decodeHTMLEntities(rawNodePath)

      if (nodePath.startsWith('$')) {
        for (const np in this.pathToNodeMap) {
          if (np.split('>').pop() === nodePath) {
            node = this.pathToNodeMap[np]?.[0]
            break
          }
        }
      } else {
        const res = this.pathToNodeMap[nodePath] || this.pathToNodeMap[getSubNodePath(nodePath)]
        const targetNode = res?.[0]
        if (targetNode && (targetNode as any).visible !== false) {
          node = targetNode
        }
      }

      if (!node) {
        const foundNodes = this.findNodesFromNodePath(nodePath)
        if (foundNodes.length > 0) {
          node = foundNodes[0]
        }
      }

      if (node) break
    }

    if (!node) {
      console.warn('getPropValue 找不到节点', data.nodePath)
      return Promise.resolve(undefined)
    }
    // 兼容宽高写法
    node.width = node.absoluteBoundingBox?.width
    node.height = node.absoluteBoundingBox?.height

    if (typeof data.getPropValue === 'string') {
      const funcName = data.getPropValue
      const regex = /\${(.*?)}/g
      if (regex.test(funcName)) {
        // 此处简化处理，更复杂的模板字符串可能需要更高级的解析器
        const value = funcName.replace(regex, (match, varName) => {
          return this.parseFuncMap[varName]?.(node) || match
        })
        setPathValue(props, propKey, value)
      } else {
        const func = this.parseFuncMap[funcName]
        if (func) {
          // 等待异步函数完成
          const value = await func(node)
          setPathValue(props, propKey, value)
        } else {
          console.warn(`[dynamicProps][getPropValue] ${funcName} 方法未定义`)
        }
      }
    } else if (typeof data.getPropValue === 'function') {
      if ((node as any).visible !== false) {
        try {
          // 等待异步函数完成
          const value = await data.getPropValue(node, this.parseFuncMap)
          setPathValue(props, propKey, value)
        } catch (e) {
          console.error(e)
          console.warn(`[dynamicProps][getPropValue] 函数执行出错`)
        }
      }
    }
    return props
  }

  // 从 component-registry/impl/componentMarkImpl.ts 重构并支持异步
  async parseArrayPropValue(propKey: string, info: IPropValueArray): Promise<any> {
    let props = {
      [propKey]: [],
    } as any

    // 并发处理所有异步操作
    const parsedPromises = info.map(data => this.parseObjectPropValue(propKey, data))
    const resolvedProps = await Promise.all(parsedPromises)

    for (const p of resolvedProps) {
      if (!p) continue
      if (typeof p[propKey] === 'object' && !Array.isArray(p[propKey])) {
        if (JSON.stringify(props[propKey]) === '[]') {
          props[propKey] = {}
        }
        props = merge({}, props, p)
      } else if (p[propKey] !== undefined) {
        props[propKey].push(p[propKey])
      }
    }
    return props
  }

  // 您的 parse 方法，已重构为异步
  async parse(): Promise<Record<string, any>> {
    let allProps = {}
    const dynamicProps = this.pluginDataObj.dynamicProps || {}
    const promises = []

    for (const propKey in dynamicProps) {
      const info = dynamicProps[propKey] as IDynamicPropsValue

      if (info === undefined) continue

      // 为每个 propKey 创建一个立即执行的异步函数 promise
      const promise = (async () => {
        let props = {}
        if (typeof info === 'object') {
          if (Array.isArray(info)) {
            if (info.find(item => item.nodePath)) {
              props = await this.parseArrayPropValue(propKey, info)
            } else {
              props[propKey] = info
            }
          } else if ('nodePath' in info && info.nodePath) {
            props = await this.parseObjectPropValue(propKey, info)
          } else {
            props[propKey] = info
          }
        } else if (typeof info === 'function') {
          const value = await info(this.node, this.parseFuncMap)
          if ((this.node as any).visible !== false) {
            props[propKey] = value
          }
        } else {
          props[propKey] = info
        }
        return props
      })()
      promises.push(promise)
    }

    // 等待所有解析任务完成
    const resolvedPropsArray = await Promise.all(promises)
    for (const props of resolvedPropsArray) {
      allProps = merge({}, allProps, props)
    }

    return {
      // config: this.pluginDataObj,
      ...allProps,
    }
  }
}
