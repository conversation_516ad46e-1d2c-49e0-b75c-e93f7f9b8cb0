// 快速测试 TinyPNG API
console.log('开始测试...');

// 测试基本的 JSON 请求
fetch('http://localhost:3000/tinify/compress', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    imageUrl: 'test'
  })
})
.then(response => {
  console.log('状态码:', response.status);
  return response.json();
})
.then(data => {
  console.log('响应:', data);
})
.catch(error => {
  console.error('错误:', error.message);
});
