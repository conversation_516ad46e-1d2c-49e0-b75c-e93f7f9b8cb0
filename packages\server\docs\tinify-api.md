# TinyPNG 压缩 API 文档

## 概述

本 API 提供基于 TinyPNG 服务的图片压缩功能，支持 AVIF、WebP、JPEG 和 PNG 格式的图片压缩。

## 端点

```
POST /tinify/compress
```

## 请求参数

### 必需参数

- `token` (string): TinyPNG API 密钥
- `imageUrl` (string) 或 `imageBuffer` (string): 图片数据
  - `imageUrl`: 图片的 URL 地址
  - `imageBuffer`: 图片的 base64 编码数据（支持 data URL 格式）

### 请求示例

#### 使用图片 URL

```bash
curl -X POST http://localhost:3000/tinify/compress \
  -H "Content-Type: application/json" \
  -d '{
    "token": "YOUR_TINYPNG_API_KEY",
    "imageUrl": "https://example.com/image.png"
  }'
```

#### 使用 base64 图片数据

```bash
curl -X POST http://localhost:3000/tinify/compress \
  -H "Content-Type: application/json" \
  -d '{
    "token": "YOUR_TINYPNG_API_KEY",
    "imageBuffer": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
  }'
```

#### 使用表单数据

```bash
curl -X POST http://localhost:3000/tinify/compress \
  -F "token=YOUR_TINYPNG_API_KEY" \
  -F "imageUrl=https://example.com/image.png"
```

## 响应

### 成功响应

- **状态码**: 200
- **Content-Type**: `image/png` (或相应的图片类型)
- **响应体**: 压缩后的图片二进制数据

### 错误响应

- **状态码**: 400, 401, 405, 500, 502, 503
- **Content-Type**: `application/json`

#### 错误响应格式

```json
{
  "error": true,
  "message": "错误描述",
  "compressionCount": 123
}
```

#### 常见错误

1. **405 Method Not Allowed**
   ```json
   {
     "error": true,
     "message": "仅支持 POST 请求"
   }
   ```

2. **400 Bad Request**
   ```json
   {
     "error": true,
     "message": "缺少 TinyPNG API token"
   }
   ```

3. **401 Unauthorized**
   ```json
   {
     "error": true,
     "message": "TinyPNG 账户错误: Credentials are invalid",
     "compressionCount": 0
   }
   ```

4. **400 Bad Request**
   ```json
   {
     "error": true,
     "message": "TinyPNG 客户端错误: Input file is corrupt",
     "compressionCount": 123
   }
   ```

## 支持的图片格式

- AVIF
- WebP
- JPEG
- PNG

## 注意事项

1. 需要有效的 TinyPNG API 密钥
2. TinyPNG 有月度压缩次数限制
3. 图片大小限制请参考 TinyPNG 官方文档
4. 压缩后的图片会直接返回二进制数据，可以直接保存为文件

## 获取 TinyPNG API 密钥

访问 [TinyPNG 开发者页面](https://tinypng.com/developers) 注册并获取 API 密钥。

## 示例代码

### JavaScript (Node.js)

```javascript
const fs = require('fs');

async function compressImage() {
  const response = await fetch('http://localhost:3000/tinify/compress', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      token: 'YOUR_TINYPNG_API_KEY',
      imageUrl: 'https://example.com/image.png'
    })
  });

  if (response.ok) {
    const buffer = await response.arrayBuffer();
    fs.writeFileSync('compressed-image.png', Buffer.from(buffer));
    console.log('图片压缩成功！');
  } else {
    const error = await response.json();
    console.error('压缩失败:', error.message);
  }
}

compressImage();
```

### Python

```python
import requests

def compress_image():
    url = 'http://localhost:3000/tinify/compress'
    data = {
        'token': 'YOUR_TINYPNG_API_KEY',
        'imageUrl': 'https://example.com/image.png'
    }
    
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        with open('compressed-image.png', 'wb') as f:
            f.write(response.content)
        print('图片压缩成功！')
    else:
        error = response.json()
        print(f'压缩失败: {error["message"]}')

compress_image()
```
