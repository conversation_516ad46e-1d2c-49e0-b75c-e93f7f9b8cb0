// 在文件顶部导入日志服务
import {ComponentMarkImpl} from 'src/libs/parseComponent'
import {config} from '../config'
import {figmaAPI} from './api'
import {commonService} from './common'
import {convertToHtmlWithReport, f2cService} from './f2c'
import {logger} from './logger'
import {oauthService} from './oauth'

export class RouterService {
  private static instance: RouterService

  // 修改为私有构造函数
  private constructor() {}

  // 单例模式
  static getInstance(): RouterService {
    if (!RouterService.instance) {
      RouterService.instance = new RouterService()
    }
    return RouterService.instance
  }
  private getParamsFromUrl(req: Request): Record<string, any> {
    const url = new URL(req.url)
    const params: Record<string, any> = {}

    url.searchParams.forEach((value, key) => {
      try {
        // 处理数组形式的参数 key[] 或 key[index]
        const arrayMatch = key.match(/^(.+?)\[(.*?)\]$/)
        if (arrayMatch) {
          const [, baseKey, arrayKey] = arrayMatch

          if (!params[baseKey]) {
            params[baseKey] = arrayKey === '' ? [] : {}
          }

          if (arrayKey === '') {
            // 处理 key[] 形式
            if (Array.isArray(params[baseKey])) {
              params[baseKey].push(this.parseValue(value))
            }
          } else {
            // 处理 key[subkey] 形式
            params[baseKey][arrayKey] = this.parseValue(value)
          }
        } else {
          // 处理普通参数，尝试解析 JSON
          params[key] = this.parseValue(value)
        }
      } catch (error) {
        logger.warn(`解析查询参数失败: ${key}=${value}`)
        params[key] = value // 解析失败时保留原始值
      }
    })

    return params
  }

  // 辅助方法：解析参数值
  private parseValue(value: string): any {
    // 尝试解析 JSON 字符串
    try {
      // 检查是否是一个有效的 JSON 字符串
      if (value.startsWith('{') || value.startsWith('[')) {
        return JSON.parse(value)
      }
      // 处理布尔值
      if (value === 'true') return true
      if (value === 'false') return false
      // 处理数字
      if (/^\d+$/.test(value)) return Number(value)
      // 处理 undefined/null
      if (value === 'undefined') return undefined
      if (value === 'null') return null
    } catch {
      // 解析失败返回原始字符串
    }
    return value
  }
  // 通用方法：从URL获取参数
  // private getParamsFromUrl(req: Request): Record<string, string> {
  //   const url = new URL(req.url)
  //   const params: Record<string, string> = {}

  //   // 遍历所有查询参数并添加到结果对象中
  //   url.searchParams.forEach((value, key) => {
  //     params[key] = value
  //   })

  //   return params
  // }

  // 通用方法：从POST请求体获取数据
  private async getParamsFromPost(req: Request): Promise<Record<string, any>> {
    const contentType = req.headers.get('content-type') || ''
    const params: Record<string, any> = {}

    // 处理 multipart/form-data
    if (contentType.includes('multipart/form-data')) {
      const formData = await req.formData()

      // 遍历所有表单字段
      formData.forEach((value, key) => {
        // 处理布尔值字符串
        if (value === 'true') {
          params[key] = true
        } else if (value === 'false') {
          params[key] = false
        } else {
          params[key] = value.toString()
        }
      })
    }
    // 处理 application/json
    else if (contentType.includes('application/json')) {
      const body = await req.json()
      Object.assign(params, body)
    }
    // 其他内容类型将返回空对象

    return params
  }

  // 处理令牌设置的通用方法
  private setupTokens(params: Record<string, any>): void {
    const access_token = params.access_token
    const personal_token = params.personal_token

    if (access_token || personal_token) {
      figmaAPI.setToken({
        accessToken: access_token || '',
        personalToken: personal_token || '',
      })
      logger.info(`设置令牌: ${JSON.stringify(figmaAPI.headers)}`)
    } else {
      logger.info('未提供令牌')
    }
  }

  // API 路由处理
  async handleApiFile(req: Request): Promise<Response> {
    try {
      const params = this.getParamsFromUrl(req)
      const fileKey = params.fileKey

      if (!fileKey) return commonService.addCorsHeaders(commonService.errorResponse('Missing fileKey', 400))

      // 设置令牌
      this.setupTokens(params)

      const data: any = await figmaAPI.getFile(fileKey)

      // 检查API返回的错误
      if (data && typeof data === 'object' && 'error' in data && data.error) {
        logger.error('Figma API返回错误:', data)
        return commonService.addCorsHeaders(
          commonService.jsonResponse(
            {
              error: true,
              message: data?.message || 'Figma API请求失败',
              status: data?.status || 500,
            },
            500,
          ),
        )
      }

      return commonService.addCorsHeaders(commonService.jsonResponse(data))
    } catch (error) {
      logger.error('获取文件时出错:', error)
      return commonService.addCorsHeaders(
        commonService.jsonResponse(
          {
            error: true,
            message: error instanceof Error ? error.message : String(error),
          },
          500,
        ),
      )
    }
  }

  async handleApiNodes(req: Request): Promise<Response> {
    const MAX_RETRIES = 3
    let retryCount = 0
    let lastError: any = null

    while (retryCount < MAX_RETRIES) {
      try {
        if (req.method === 'POST') {
          return await this.handleApiNodesPost(req)
        } else {
          return await this.handleApiNodesGet(req)
        }
      } catch (error: any) {
        lastError = error
        retryCount++

        logger.error(`处理节点请求时出错 (重试 ${retryCount}/${MAX_RETRIES}):`, {
          error: error.message,
          stack: error.stack,
          method: req.method,
          url: req.url,
        })

        if (retryCount >= MAX_RETRIES) {
          logger.error(`处理节点请求失败，已达到最大重试次数: ${MAX_RETRIES}`)
          return commonService.addCorsHeaders(
            commonService.jsonResponse(
              {
                error: true,
                message: lastError instanceof Error ? lastError.message : String(lastError),
                retries: retryCount,
              },
              502, // Bad Gateway
            ),
          )
        }

        // 等待一段时间再重试
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
      }
    }

    // 理论上不会执行到这里
    return commonService.addCorsHeaders(
      commonService.jsonResponse(
        {
          error: true,
          message: 'Unexpected error occurred',
        },
        500,
      ),
    )
  }

  // 抽象出参数处理逻辑
  private async extractNodeParams(
    params: Record<string, any>,
  ): Promise<{fileKey: string; nodeIds: string; format: string; scale: string; option?: any}> {
    let fileKey = params.fileKey || ''
    let nodeIds = params.nodeIds || ''
    const format = params.format || ''
    const option = params.option || ''
    const scale = params.scale || '1'
    // 处理 Figma 链接（业务逻辑）
    if (params.figma_link) {
      const parsed = commonService.parseFigmaLink(params.figma_link)
      // 如果已经有fileKey和nodeIds，则不覆盖
      fileKey = fileKey || parsed.fileKey
      nodeIds = nodeIds || parsed.nodeIds
    }

    return {fileKey, nodeIds, format, option, scale}
  }

  private async handleApiNodesPost(req: Request): Promise<Response> {
    // 检查内容类型
    const contentType = req.headers.get('content-type') || ''
    if (!contentType.includes('multipart/form-data') && !contentType.includes('application/json')) {
      return commonService.addCorsHeaders(
        commonService.jsonResponse(
          {
            error: '不支持的内容类型',
            message: `需要 multipart/form-data 或 application/json，收到 ${contentType}`,
          },
          415,
        ),
      )
    }

    try {
      // 使用通用方法获取参数
      const params = await this.getParamsFromPost(req)

      // 使用抽象方法提取参数
      const {fileKey, nodeIds, format} = await this.extractNodeParams(params)

      // 设置令牌
      this.setupTokens(params)

      logger.info(`处理 Figma 节点请求(POST): fileKey=${fileKey}, nodeIds=${nodeIds}, format=${format}`)
      return await this.processNodes(fileKey, nodeIds, format, params, req)
    } catch (error) {
      logger.error('处理 POST 请求时出错:', error)
      return commonService.addCorsHeaders(
        commonService.jsonResponse(
          {
            error: true,
            message: error instanceof Error ? error.message : String(error),
          },
          500,
        ),
      )
    }
  }

  private async handleApiNodesGet(req: Request): Promise<Response> {
    const params = this.getParamsFromUrl(req)

    // 使用抽象方法提取参数
    const {fileKey, nodeIds, format, option = {}} = await this.extractNodeParams(params)

    // 设置令牌
    this.setupTokens(params)

    logger.info(
      `处理 Figma 节点请求(GET): fileKey=${fileKey}, nodeIds=${nodeIds}, format=${format}, option=${JSON.stringify(option)} params=${JSON.stringify(params)}`,
    )
    // react html vue
    // if (params.uiFramework) {
    //   if (!params.option) {
    //     params.option = {}
    //   }
    //   if (params.uiFramework === 'react') {
    //     params.option['uiFramework'] = 'react'
    //   }
    //   if (params.uiFramework === 'vue') {
    //     params.option['uiFramework'] = 'vue'
    //   }
    //   if (params.uiFramework === 'html') {
    //     params.option['cssFramework'] = 'inlinecss'
    //   }
    // }
    // // tailwind cssmodules inlineStyle
    // if (params.cssFramework) {
    //   if (params.cssFramework === 'tailwindcss') {
    //     params.option['cssFramework'] = 'tailwindcss'
    //     params.option['useInlineStyle'] = true
    //   }
    //   if (params.cssFramework === 'cssmodules') {
    //     params.option['cssFramework'] = 'cssmodules'
    //   }
    //   if (params.cssFramework === 'inlineStyle') {
    //     params.option['useInlineStyle'] = true
    //   }
    // }

    return await this.processNodes(fileKey, nodeIds, format, params, req)
  }

  // 在 processNodes 方法中使用日志服务
  private async processNodes(
    fileKey: string,
    nodeIds: string,
    format: string,
    params?: Record<string, any>,
    req?: Request,
  ): Promise<Response> {
    const {option, ...urlParams} = params || {}
    if (!fileKey) {
      logger.warn('缺少 fileKey 参数')
      return commonService.addCorsHeaders(commonService.errorResponse('Missing fileKey', 400))
    }

    const finalNodeIds = nodeIds ? nodeIds.split(',') : []
    if (finalNodeIds.length === 0) {
      logger.warn('未提供 nodeIds 参数')
      return commonService.addCorsHeaders(commonService.errorResponse('No nodeIds provided', 400))
    }

    try {
      logger.info(`调用 Figma API 获取节点: fileKey=${fileKey}, nodeIds=${finalNodeIds.join(',')}`)
      const result = await figmaAPI.getFileNodes(fileKey, finalNodeIds, urlParams)

      // 检查是否有授权问题
      if (result && 'error' in result) {
        logger.error(`Figma API 请求失败: ${result.message}`)

        // 处理授权错误 (403)
        if (result.status === 403) {
          logger.warn(`访问被拒绝 (403): fileKey=${fileKey}`)
          return commonService.addCorsHeaders(
            commonService.jsonResponse(
              {
                code: 403,
                error: true,
                msg: '访问被拒绝',
                detail: '您没有权限访问此 Figma 文件。请确认：1. 文件已公开分享 2. 您的访问令牌有效且具有足够权限',
              },
              200,
            ),
          )
        }

        // 处理其他 API 错误
        return commonService.addCorsHeaders(
          commonService.jsonResponse(
            {
              code: result.status || 500,
              error: true,
              msg: 'Figma API 请求失败',
              detail: result.message,
            },
            result.status || 500,
          ),
        )
      }

      // 检查返回的数据是否有效
      if (!result.nodes || Object.keys(result.nodes).length === 0) {
        logger.warn(`未找到节点: fileKey=${fileKey}, nodeIds=${finalNodeIds.join(',')}`)
        return commonService.addCorsHeaders(
          commonService.jsonResponse(
            {
              code: 404,
              error: true,
              msg: '未找到节点',
              detail: '请求的节点不存在或无法访问',
            },
            404,
          ),
        )
      }

      if (result.nodes[finalNodeIds[0].replace('-', ':')].document.type === 'CANVAS') {
        return commonService.addCorsHeaders(
          commonService.jsonResponse(
            {
              code: 500,
              error: true,
              msg: 'nodeId必须是具体的图层节点',
              detail: '请求的节点不能是顶层Canvas节点，请使用具体图层的node-id',
            },
            500,
          ),
        )
      }
      if (format === 'html' || format === 'code') {
        logger.info('开始转换为 HTML')
        if (option && option.cssFramework === 'tailwindcss') {
          option.useInlineStyle = true
        }
        const res = await convertToHtmlWithReport({
          nodeIds,
          nodesResult: result,
          option,
          params,
          req,
        })
        if (res) {
          logger.info(`HTML 转换成功，格式: ${format} ${JSON.stringify(option)}`)
          return commonService.addCorsHeaders(
            commonService.htmlResponse(await f2cService.processCodeWithImages(res!, fileKey)),
          )
        } else {
          logger.error('HTML 转换失败')
          return commonService.addCorsHeaders(commonService.jsonResponse({code: 500, msg: '转换HTML失败'}, 500))
        }
      } else if (format === 'files') {
        logger.info('开始转换为 files JSON')
        const res = await convertToHtmlWithReport({
          nodeIds,
          nodesResult: result,
          option,
          params,
          req,
        })
        if (res) {
          logger.info(`files JSON 转换成功`)
          return commonService.addCorsHeaders(
            commonService.jsonResponse(await f2cService.processFilesWithImages(res!, fileKey, option)),
          )
        } else {
          logger.error('files JSON 转换失败')
          return commonService.addCorsHeaders(commonService.jsonResponse({code: 500, msg: '转换 MCP JSON 失败'}, 500))
        }
      } else if (format === 'allFiles') {
        logger.info('开始转换为 allFiles JSON')
        const res = await convertToHtmlWithReport({
          nodeIds,
          nodesResult: result,
          option,
          params,
          req,
        })
        if (res) {
          logger.info(`allFiles JSON 转换成功`)
          return commonService.addCorsHeaders(
            commonService.jsonResponse(await f2cService.processAllFilesWithImages(res!, fileKey, option)),
          )
        } else {
          logger.error('allFiles JSON 转换失败')
          return commonService.addCorsHeaders(commonService.jsonResponse({code: 500, msg: '转换 MCP JSON 失败'}, 500))
        }
      } else {
        return commonService.addCorsHeaders(commonService.jsonResponse(result))
      }
    } catch (error) {
      logger.error('处理节点时出错:', error)
      return commonService.addCorsHeaders(
        commonService.jsonResponse(
          {
            error: true,
            message: error instanceof Error ? error.message : String(error),
            fileKey,
            nodeIds: finalNodeIds,
          },
          500,
        ),
      )
    }
  }

  async handleApiMe(req: Request): Promise<Response> {
    try {
      const params = this.getParamsFromUrl(req)
      if (!params.access_token && !params.personal_token)
        return commonService.addCorsHeaders(commonService.errorResponse('Missing accessToken or personalToken', 400))

      // 设置令牌
      this.setupTokens(params)
      const data = await figmaAPI.getMe()

      // 检查API返回的错误
      if (data && typeof data === 'object' && 'error' in data && data.error) {
        logger.error('Figma API返回错误:', data)
        return commonService.addCorsHeaders(
          commonService.jsonResponse(
            {
              error: true,
              message: data.message || 'Figma API请求失败',
              status: data.status || 500,
            },
            500,
          ),
        )
      }

      return commonService.addCorsHeaders(commonService.jsonResponse(data))
    } catch (error) {
      logger.error('获取个人信息时出错:', error)
      return commonService.addCorsHeaders(
        commonService.jsonResponse(
          {
            error: true,
            message: error instanceof Error ? error.message : String(error),
          },
          500,
        ),
      )
    }
  }

  async handleApiImages(req: Request): Promise<Response> {
    const MAX_RETRIES = 3
    let retryCount = 0
    let lastError: any = null

    while (retryCount < MAX_RETRIES) {
      try {
        const params = this.getParamsFromUrl(req)
        const {fileKey, nodeIds} = await this.extractNodeParams(params)

        if (!fileKey || !nodeIds) {
          logger.warn('缺少必要参数' + JSON.stringify({fileKey, nodeIds}))
          return commonService.addCorsHeaders(commonService.errorResponse('Missing fileKey or nodeIds', 400))
        }

        // 设置令牌
        this.setupTokens(params)
        logger.info(`获取图像请求: fileKey=${fileKey}, nodeIds=${nodeIds}, 重试次数=${retryCount}`)

        // 添加超时处理
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

        const data = await figmaAPI.getImage(fileKey, nodeIds.split(','), {
          ...params,
          signal: controller.signal,
        } as any)

        clearTimeout(timeoutId)

        // 检查API返回的错误
        if (data && typeof data === 'object' && 'error' in data && data.error) {
          logger.error('Figma API返回错误:', data)
          return commonService.addCorsHeaders(
            commonService.jsonResponse(
              {
                error: true,
                message: data.message || 'Figma API请求失败',
                status: data.status || 500,
              },
              data.status || 500,
            ),
          )
        }

        logger.info(`成功获取图像数据`)
        return commonService.addCorsHeaders(commonService.jsonResponse(data))
      } catch (error: any) {
        lastError = error
        retryCount++

        if (error.name === 'AbortError') {
          logger.warn(`请求超时，重试次数: ${retryCount}`)
        } else {
          logger.error(`获取图像时出错: ${error.message}`, {stack: error.stack})
        }

        // 最后一次重试仍然失败
        if (retryCount >= MAX_RETRIES) {
          logger.error(`获取图像失败，已达到最大重试次数: ${MAX_RETRIES}`)
          return commonService.addCorsHeaders(
            commonService.jsonResponse(
              {
                error: true,
                message: lastError instanceof Error ? lastError.message : String(lastError),
                retries: retryCount,
              },
              502, // Bad Gateway
            ),
          )
        }

        // 等待一段时间再重试
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
      }
    }

    // 理论上不会执行到这里
    return commonService.addCorsHeaders(
      commonService.jsonResponse(
        {
          error: true,
          message: 'Unexpected error occurred',
        },
        500,
      ),
    )
  }

  async handleApiProjectFiles(req: Request): Promise<Response> {
    try {
      const params = this.getParamsFromUrl(req)
      const projectId = params.projectId

      if (!projectId) return commonService.addCorsHeaders(commonService.errorResponse('Missing projectId', 400))

      // 设置令牌
      this.setupTokens(params)

      const data: any = await figmaAPI.getProjectFiles(projectId)

      // 检查API返回的错误
      if (data && typeof data === 'object' && 'error' in data && data.error) {
        logger.error('Figma API返回错误:', data)
        return commonService.addCorsHeaders(
          commonService.jsonResponse(
            {
              error: true,
              message: data.message || 'Figma API请求失败',
              status: data.status || 500,
            },
            500,
          ),
        )
      }

      return commonService.addCorsHeaders(commonService.jsonResponse(data))
    } catch (error) {
      logger.error('获取项目文件时出错:', error)
      return commonService.addCorsHeaders(
        commonService.jsonResponse(
          {
            error: true,
            message: error instanceof Error ? error.message : String(error),
          },
          500,
        ),
      )
    }
  }

  // OAuth 路由处理方法
  async handleAuth(type: 'page' | 'json' | 'astro'): Promise<Response> {
    return await oauthService.handleAuth(type)
  }

  async handleOAuthCallback(req: Request): Promise<Response> {
    const params = this.getParamsFromUrl(req)
    const code = params.code || ''

    if (!code) return commonService.addCorsHeaders(commonService.errorResponse('No code provided', 400))

    const response = await oauthService.handleCallback(code)
    return commonService.addCorsHeaders(response)
  }

  async handleOAuthPageCallback(req: Request): Promise<Response> {
    const params = this.getParamsFromUrl(req)
    const code = params.code || ''

    if (!code) return commonService.addCorsHeaders(commonService.errorResponse('No code provided', 400))

    // 获取OAuth响应数据
    const tokenData = await oauthService.getCallbackData(code)
    logger.info(`tokenData: ${JSON.stringify(tokenData)}`)
    // 使用模板渲染HTML响应
    const html = await commonService.renderTemplate('oauth_success', {
      accessToken: tokenData.access_token || '',
      tokenType: tokenData.token_type || '',
      expiresIn: tokenData.expires_in || 0,
      refreshToken: tokenData.refresh_token || '',
    })

    return commonService.addCorsHeaders(
      new Response(html, {
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
        },
      }),
    )
  }

  async handleAuthUrl(): Promise<Response> {
    const authUrl = `${config.figma.oauthUrl}?client_id=${config.figma.clientId}&redirect_uri=${config.figma.redirectUri}&scope=files:read&state=random&response_type=code`
    return commonService.addCorsHeaders(
      commonService.jsonResponse({
        url: authUrl,
        success: true,
      }),
    )
  }

  // 其他路由处理方法
  handleHome(): Response {
    return commonService.addCorsHeaders(new Response('Welcome to Figma OAuth Server'))
  }

  handleNotFound(): Response {
    return commonService.addCorsHeaders(new Response('Not Found', {status: 404}))
  }

  handleOptions(): Response {
    return new Response(null, {headers: commonService.getCorsHeaders()})
  }

  async handleParseComponent(req: Request) {
    const params = this.getParamsFromUrl(req)

    // 使用抽象方法提取参数
    const {fileKey, nodeIds, format} = await this.extractNodeParams(params)

    // 设置令牌
    this.setupTokens(params)

    logger.info(
      `处理 Figma 节点请求(GET): fileKey=${fileKey}, nodeIds=${nodeIds}, format=${format}, params=${JSON.stringify(params)}`,
    )

    const {option, ...urlParams} = params || {}
    if (!fileKey) {
      logger.warn('缺少 fileKey 参数')
      return commonService.addCorsHeaders(commonService.errorResponse('Missing fileKey', 400))
    }

    const finalNodeIds = nodeIds ? nodeIds.split(',') : []
    if (finalNodeIds.length === 0) {
      logger.warn('未提供 nodeIds 参数')
      return commonService.addCorsHeaders(commonService.errorResponse('No nodeIds provided', 400))
    }

    let result
    try {
      logger.info(`调用 Figma API 获取节点: fileKey=${fileKey}, nodeIds=${finalNodeIds.join(',')}`)

      urlParams.plugin_data = 'shared'
      result = await figmaAPI.getFileNodes(fileKey, finalNodeIds, urlParams)

      // 检查是否有授权问题
      if (result && 'error' in result) {
        logger.error(`Figma API 请求失败: ${result.message}`)

        // 处理授权错误 (403)
        if (result.status === 403) {
          logger.warn(`访问被拒绝 (403): fileKey=${fileKey}`)
          return commonService.addCorsHeaders(
            commonService.jsonResponse(
              {
                code: 403,
                error: true,
                msg: '访问被拒绝',
                detail: '您没有权限访问此 Figma 文件。请确认：1. 文件已公开分享 2. 您的访问令牌有效且具有足够权限',
              },
              200,
            ),
          )
        }

        // 处理其他 API 错误
        return commonService.addCorsHeaders(
          commonService.jsonResponse(
            {
              code: result.status || 500,
              error: true,
              msg: 'Figma API 请求失败',
              detail: result.message,
            },
            result.status || 500,
          ),
        )
      }

      // 检查返回的数据是否有效
      if (!result.nodes || Object.keys(result.nodes).length === 0) {
        logger.warn(`未找到节点: fileKey=${fileKey}, nodeIds=${finalNodeIds.join(',')}`)
        return commonService.addCorsHeaders(
          commonService.jsonResponse(
            {
              code: 404,
              error: true,
              msg: '未找到节点',
              detail: '请求的节点不存在或无法访问',
            },
            404,
          ),
        )
      }

      if (result.nodes[finalNodeIds[0].replace('-', ':')].document.type === 'CANVAS') {
        return commonService.addCorsHeaders(
          commonService.jsonResponse(
            {
              code: 500,
              error: true,
              msg: 'nodeId必须是具体的图层节点',
              detail: '请求的节点不能是顶层Canvas节点，请使用具体图层的node-id',
            },
            500,
          ),
        )
      }
    } catch (error) {
      logger.error('处理节点时出错:', error)
      return commonService.addCorsHeaders(
        commonService.jsonResponse(
          {
            error: true,
            message: error instanceof Error ? error.message : String(error),
            fileKey,
            nodeIds: finalNodeIds,
          },
          500,
        ),
      )
    }
    const node = result.nodes[nodeIds]?.document
    const componentMark = node?.sharedPluginData?.['baidu_yy_f2c_2023.']?.f2c_component_mark
    if (!componentMark) {
      const msg = '未找到组件标记，请检查节点是否包含 F2C 组件标记'
      logger.error(msg)
      return commonService.addCorsHeaders(
        commonService.jsonResponse(
          {
            error: true,
            message: msg,
            fileKey,
            nodeIds: finalNodeIds,
          },
          500,
        ),
      )
    }
    const componentMarkImpl = new ComponentMarkImpl(componentMark, node, params)
    const json = await componentMarkImpl.parse()

    return commonService.addCorsHeaders(commonService.jsonResponse(json))
  }
}

// 导出单例实例
export const routerService = RouterService.getInstance()
