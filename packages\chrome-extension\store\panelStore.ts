import {setAltKey, setDeepSelectionKey} from '@/lib/keylock'
import {BaseStore} from './baseStore'

export class PanelStore extends BaseStore {
  constructor() {
    super()
    this.init()
  }

  isExpend = true
  position = {x: 0, y: 0}
  lockDeepSelection = false
  lockLockAltKey = false
  enablePreview = true

  private init() {
    const savedState = localStorage.getItem('f2c:pannelSettings')
    if (savedState !== null) {
      const parsed = JSON.parse(savedState)
      this.isExpend = parsed.isExpend
      this.position = parsed.position || this.getDefaultPosition()
      this.lockDeepSelection = parsed.lockDeepSelection || false
      this.lockLockAltKey = parsed.lockLockAltKey || false
      this.enablePreview = parsed.enablePreview ?? true
      this.position = this.ensurePositionIsInViewport(this.position)
    } else {
      // 如果没有保存状态，设置默认位置为右上角
      this.position = this.getDefaultPosition()
    }
    this.saveToLocalStorage()
  }

  private getDefaultPosition() {
    // 获取窗口尺寸，设置面板在右上角
    // 假设面板宽度约为 300px，留一些边距
    const panelWidth = 300
    const margin = 20

    return {
      x: window.innerWidth - panelWidth - margin,
      y: margin,
    }
  }

  private ensurePositionIsInViewport(position: { x: number; y: number }) {
    const panelWidth = 300 // Consistent with getDefaultPosition
    const panelHeight = 150 // A reasonable estimate for panel height
    const margin = 20 // Consistent with getDefaultPosition

    let { x: newX, y: newY } = position

    if (newX < margin) {
      newX = margin
    }
    if (newX > window.innerWidth - panelWidth - margin) {
      newX = window.innerWidth - panelWidth - margin
    }

    if (newY < margin) {
      newY = margin
    }
    if (newY > window.innerHeight - panelHeight - margin) {
      newY = window.innerHeight - panelHeight - margin
    }

    return { x: newX, y: newY }
  }

  setIsExpend(value: boolean) {
    this.isExpend = value
    this.saveToLocalStorage()
  }

  setPosition(x: number, y: number) {
    this.position = {x, y}
    this.saveToLocalStorage()
  }

  setLockDeepSelection(lock: boolean) {
    this.lockDeepSelection = lock
    setDeepSelectionKey(lock)
    this.saveToLocalStorage()
  }

  setLockAltKey(lock: boolean) {
    this.lockLockAltKey = lock
    setAltKey(lock)
    this.saveToLocalStorage()
  }

  setEnablePreview(enablePreview: boolean) {
    this.enablePreview = enablePreview
    this.saveToLocalStorage()
  }

  private saveToLocalStorage() {
    localStorage.setItem(
      'f2c:pannelSettings',
      JSON.stringify({
        isExpend: this.isExpend,
        position: this.position,
        lockDeepSelection: this.lockDeepSelection,
        lockLockAltKey: this.lockLockAltKey,
        enablePreview: this.enablePreview,
      }),
    )
  }
}

const panelStore = new PanelStore()
export default panelStore
