import fs from 'node:fs'
import path from 'node:path'
import {marked} from 'marked'
import {config} from './config'
import {LogLevel, logger} from './services/logger'
import {routerService} from './services/router'

// 设置日志级别，可以根据环境变量或配置文件调整
logger.setLogLevel(process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG)

const serverConfig: any = {
  port: config.port,
  idleTimeout: 90, // 增加服务器空闲超时时间为30秒

  // 定义路由
  routes: {
    // API 路由
    '/api/file': (req: Request) => routerService.handleApiFile(req),
    '/api/nodes': (req: Request) => routerService.handleApiNodes(req),
    '/api/images': (req: Request) => routerService.handleApiImages(req),
    '/api/project-files': (req: Request) => routerService.handleApiProjectFiles(req),
    '/api/me': (req: Request) => routerService.handleApiMe(req),
    '/api/parse-component': (req: Request) => routerService.handleParseComponent(req),
    // TinyPNG 压缩路由
    '/tinify/compress': (req: Request) => routerService.handleTinifyCompress(req),
    // OAuth 路由
    '/auth': () => routerService.handleAuth('json'),
    '/auth/page': () => routerService.handleAuth('page'),
    '/auth/astro': () => routerService.handleAuth('astro'),
    '/oauth/callback': (req: Request) => routerService.handleOAuthPageCallback(req),
    '/auth/url': () => routerService.handleAuthUrl(),
    // Docs 路由
    '/docs': async () => {
      const filePath = path.join(process.cwd(), 'docs', 'api.md')
      try {
        const markdown = fs.readFileSync(filePath, 'utf8')
        const html = await marked(markdown) // 将 Markdown 转换为 HTML
        return new Response(html, {
          headers: {
            'Content-Type': 'text/html; charset=utf-8', // 设置 Content-Type 为 text/html
          },
        })
      } catch (error) {
        logger.error('读取 Markdown 文件失败:', error)
        return new Response('Failed to load API documentation', {status: 500})
      }
    },
    // 首页
    '/': () => routerService.handleHome(),
  },

  // 处理未匹配的路由和 OPTIONS 请求
  fetch(req: {method: string}) {
    // 处理 OPTIONS 请求
    if (req.method === 'OPTIONS') {
      return routerService.handleOptions()
    }
    return routerService.handleNotFound()
  },
}

// 根据配置决定是否启用 HTTPS
if (config.enableHttps) {
  try {
    serverConfig.tls = {
      key: fs.readFileSync(path.join(process.cwd(), 'certs', 'key.pem')),
      cert: fs.readFileSync(path.join(process.cwd(), 'certs', 'cert.pem')),
    }
    logger.info('✅ HTTPS 已启用')
  } catch (error) {
    logger.error('❌ HTTPS 证书加载失败，将使用 HTTP:', error)
    logger.info('💡 请运行 ./generate-certs.sh 生成证书或设置 ENABLE_HTTPS=false')
  }
} else {
  logger.info('ℹ️ HTTPS 已禁用，使用 HTTP 模式')
}

const server = Bun.serve(serverConfig)

const protocol = config.enableHttps ? 'https' : 'http'
logger.info(`🦊 服务器已启动，运行在 ${protocol}://localhost:${server.port}`)

if (config.enableHttps) {
  logger.info('📋 HTTPS 模式需要证书文件：certs/key.pem 和 certs/cert.pem')
  logger.info('🔧 如需禁用 HTTPS，请设置环境变量：ENABLE_HTTPS=false')
} else {
  logger.info('🔧 如需启用 HTTPS，请设置环境变量：ENABLE_HTTPS=true')
}
