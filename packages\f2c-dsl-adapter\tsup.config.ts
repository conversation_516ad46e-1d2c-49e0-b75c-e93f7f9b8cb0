import {defineConfig} from 'tsup'

export default defineConfig(({watch}) => {
  const isDev = !!watch
  return {
    entry: ['src/index.ts'],
    // outDir: '/Users/<USER>/Desktop/Develop/designToAI/i2c-server/packages/html-dsl/output',
    format: ['esm'],

    splitting: false, // 确保不拆分代码
    sourcemap: isDev,
    bundle: true, // 确保打包所有依赖
    // minify: !isDev,
    clean: true,
    // dts: !isDev
    //   ? {
    //       // 配置 dts 生成选项，忽略未使用变量的警告
    //       compilerOptions: {
    //         noUnusedLocals: false,
    //         noUnusedParameters: false,
    //       },
    //     }
    //   : false,
    // dts: {
    //   compilerOptions: {
    //     noUnusedLocals: false,
    //     noUnusedParameters: false,
    //   },
    // },
    dts: false,
    shims: true,
    treeshake: true, // 启用树摇优化
    // outExtension: () => ({js: '.js'}), // 确保输出单一的js文件
    noExternal: [/.*/], // 将所有依赖都打包进来，包括node_modules
    esbuildOptions(options) {
      // 添加 pure 注释，移除 console.log 等调用
      options.pure = ['console.log', 'console.info', 'console.debug', 'console.warn']
      // 或者使用 drop 选项完全移除
      options.drop = ['console', 'debugger']
    },
  }
})
