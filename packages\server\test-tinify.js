// TinyPNG 压缩 API 测试脚本
const fs = require('fs');

// 测试用的小图片 base64 数据 (1x1 像素的透明 PNG)
const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

// 测试用的图片 URL
const testImageUrl = 'https://tinypng.com/images/panda-happy.png';

async function testTinifyAPI() {
  console.log('🧪 开始测试 TinyPNG 压缩 API...\n');

  // 测试 1: 缺少 token
  console.log('📋 测试 1: 缺少 token');
  try {
    const response = await fetch('http://localhost:3000/tinify/compress', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imageUrl: testImageUrl
      })
    });

    const result = await response.json();
    console.log(`   状态码: ${response.status}`);
    console.log(`   响应: ${JSON.stringify(result, null, 2)}`);
    console.log('   ✅ 正确返回错误信息\n');
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}\n`);
  }

  // 测试 2: 缺少图片数据
  console.log('📋 测试 2: 缺少图片数据');
  try {
    const response = await fetch('http://localhost:3000/tinify/compress', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: 'test-token'
      })
    });

    const result = await response.json();
    console.log(`   状态码: ${response.status}`);
    console.log(`   响应: ${JSON.stringify(result, null, 2)}`);
    console.log('   ✅ 正确返回错误信息\n');
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}\n`);
  }

  // 测试 3: 使用 GET 方法（应该失败）
  console.log('📋 测试 3: 使用 GET 方法');
  try {
    const response = await fetch('http://localhost:3000/tinify/compress?token=test&imageUrl=test', {
      method: 'GET'
    });

    const result = await response.json();
    console.log(`   状态码: ${response.status}`);
    console.log(`   响应: ${JSON.stringify(result, null, 2)}`);
    console.log('   ✅ 正确拒绝 GET 请求\n');
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}\n`);
  }

  // 测试 4: 使用无效的 TinyPNG token
  console.log('📋 测试 4: 使用无效的 TinyPNG token');
  try {
    const response = await fetch('http://localhost:3000/tinify/compress', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: 'invalid-token',
        imageBuffer: testImageBase64
      })
    });

    if (response.ok) {
      console.log('   ⚠️  意外成功 - 可能 token 有效或网络问题');
    } else {
      const result = await response.json();
      console.log(`   状态码: ${response.status}`);
      console.log(`   响应: ${JSON.stringify(result, null, 2)}`);
      console.log('   ✅ 正确处理无效 token\n');
    }
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}\n`);
  }

  console.log('🎉 API 基础功能测试完成！');
  console.log('\n📝 注意事项:');
  console.log('   - 要测试实际压缩功能，需要提供有效的 TinyPNG API key');
  console.log('   - 可以在 https://tinypng.com/developers 获取 API key');
  console.log('   - 将有效的 API key 替换到测试脚本中进行完整测试');
}

// 如果提供了有效的 TinyPNG API key，可以测试实际压缩功能
async function testWithValidToken(apiKey) {
  console.log('\n🔑 使用有效 API key 测试实际压缩功能...\n');

  console.log('📋 测试: 压缩 base64 图片');
  try {
    const response = await fetch('http://localhost:3000/tinify/compress', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: apiKey,
        imageBuffer: testImageBase64
      })
    });

    if (response.ok) {
      const buffer = await response.arrayBuffer();
      fs.writeFileSync('test-compressed-base64.png', Buffer.from(buffer));
      console.log(`   ✅ 压缩成功！文件大小: ${buffer.byteLength} bytes`);
      console.log('   📁 已保存为: test-compressed-base64.png\n');
    } else {
      const result = await response.json();
      console.log(`   ❌ 压缩失败: ${result.message}\n`);
    }
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}\n`);
  }

  console.log('📋 测试: 压缩 URL 图片');
  try {
    const response = await fetch('http://localhost:3000/tinify/compress', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: apiKey,
        imageUrl: testImageUrl
      })
    });

    if (response.ok) {
      const buffer = await response.arrayBuffer();
      fs.writeFileSync('test-compressed-url.png', Buffer.from(buffer));
      console.log(`   ✅ 压缩成功！文件大小: ${buffer.byteLength} bytes`);
      console.log('   📁 已保存为: test-compressed-url.png\n');
    } else {
      const result = await response.json();
      console.log(`   ❌ 压缩失败: ${result.message}\n`);
    }
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}\n`);
  }
}

// 运行测试
async function runTests() {
  await testTinifyAPI();

  // 如果你有有效的 TinyPNG API key，取消注释下面的行并替换为你的 key
  // await testWithValidToken('YOUR_TINYPNG_API_KEY_HERE');
}

runTests().catch(console.error);
