// 简单的 API 测试
const http = require('http');

function testAPI() {
  const postData = JSON.stringify({
    imageUrl: 'test'
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/tinify/compress',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应头: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('响应体:', data);
    });
  });

  req.on('error', (e) => {
    console.error(`请求遇到问题: ${e.message}`);
  });

  req.write(postData);
  req.end();
}

console.log('测试 TinyPNG API...');
testAPI();
