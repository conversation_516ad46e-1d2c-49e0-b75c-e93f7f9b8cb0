import {Button} from '@/components/ui/button'
import {usePluginExecutor} from '@/hooks/usePluginExecutor'
import type {PluginData} from '@/hooks/usePluginInstall'
import pluginStore from '@/store/pluginStore'
import {Plus, Puzzle} from 'lucide-react'
import type React from 'react'
import {useState} from 'react'
import PluginInstaller from './PluginInstaller'
import PluginItem from './PluginItem'

export const PluginsSection: React.FC = () => {
  const [isImporterShown, setIsImporterShown] = useState(false)
  const {plugins, activePluginSource} = pluginStore.state
  const {activePlugin, isLoading, unloadPlugin} = usePluginExecutor()

  const installedPlugins = Object.values(plugins)

  const handleInstalled = (pluginData: PluginData) => {
    console.log('🎉 PluginsSection: Plugin installation callback received:', pluginData)

    console.log('💾 PluginsSection: Adding plugin to store...')
    pluginStore.addPlugin(pluginData)
    console.log('✅ PluginsSection: Plugin added to store successfully')

    // Set as active if no active plugin
    if (!activePluginSource) {
      console.log('🎯 PluginsSection: No active plugin, setting this as active...')
      pluginStore.setActivePlugin(pluginData.source)
      console.log('✅ PluginsSection: Plugin set as active')
    } else {
      console.log('ℹ️ PluginsSection: Active plugin already exists:', activePluginSource)
    }

    console.log('🔄 PluginsSection: Hiding installer...')
    setIsImporterShown(false)
    console.log('🎊 PluginsSection: Installation process completed!')
  }

  const handleUpdated = (pluginData: PluginData) => {
    pluginStore.updatePlugin(pluginData)
  }

  const handleActiveChange = (source: string, checked: boolean) => {
    if (checked) {
      pluginStore.setActivePlugin(source)
    } else if (activePluginSource === source) {
      pluginStore.setActivePlugin(null)
    }
  }

  const handleRemove = async (source: string) => {
    try {
      await unloadPlugin(source)
      pluginStore.removePlugin(source)
      if (activePluginSource === source) {
        pluginStore.setActivePlugin(null)
      }
    } catch (error) {
      console.error('Failed to remove plugin:', error)
    }
  }

  const showImporter = () => {
    setIsImporterShown(true)
  }

  const hideImporter = () => {
    setIsImporterShown(false)
  }

  return (
    <div className="w-full p-2">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Puzzle className="h-4 w-4 text-primary" />
          <h3 className="text-xs font-medium">插件管理</h3>
        </div>
        <Plus
          className="h-3 w-3 cursor-pointer"
          onClick={() => {
            if (!isLoading) {
              showImporter()
            }
          }}
        />
      </div>

      {/* Active Plugin Info */}
      {activePluginSource && (
        <div className="text-xs text-muted-foreground mb-2 truncate">
          当前活跃: {plugins[activePluginSource]?.name || '未知'}
        </div>
      )}

      {/* Content */}
      <div className="space-y-2">
        {isImporterShown && (
          <div className="p-2 bg-muted/30 rounded border border-dashed">
            <PluginInstaller onInstalled={handleInstalled} onCancel={hideImporter} />
          </div>
        )}

        {installedPlugins.length === 0 ? (
          <div className="text-center py-6">
            <div className="mx-auto w-10 h-10 bg-muted rounded-full flex items-center justify-center mb-2">
              <Puzzle className="h-5 w-5 text-muted-foreground" />
            </div>
            <div className="text-xs font-medium text-muted-foreground mb-1">暂无插件</div>
            <div className="text-xs text-muted-foreground/70 mb-2">安装您的第一个插件</div>
            {/* <Button onClick={showImporter} variant="outline" size="sm" className="h-7 text-xs">
              <Plus className="h-3 w-3 mr-1" />
              安装插件
            </Button> */}
          </div>
        ) : (
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground mb-2">已安装 {installedPlugins.length} 个插件</div>

            {installedPlugins.map(plugin => (
              <PluginItem
                key={plugin.source}
                name={plugin.name}
                source={plugin.source}
                checked={plugin.source === activePluginSource}
                onUpdated={handleUpdated}
                onChange={checked => handleActiveChange(plugin.source, checked)}
                onRemove={() => handleRemove(plugin.source)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default PluginsSection
