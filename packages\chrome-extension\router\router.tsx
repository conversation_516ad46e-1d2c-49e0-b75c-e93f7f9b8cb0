import {Suspense, lazy} from 'react'
import {Navigate, RouterProvider, createMemoryRouter} from 'react-router-dom'

// 懒加载路由页面组件
const Dimension = lazy(() => import('@/entrypoints/popup/Dimension'))
const CutDiagram = lazy(() => import('@/components/CutDiagram'))
import Layout from '@/components/Layout'
import PluginsSection from '@/components/PluginsSection'

// 加载中组件
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-4">
    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
    <span className="ml-2 text-xs text-gray-600">加载中...</span>
  </div>
)

// 错误回退组件
const ErrorFallback = ({error, resetErrorBoundary}: {error: Error; resetErrorBoundary: () => void}) => (
  <div className="flex flex-col items-center justify-center p-4 text-center">
    <div className="text-red-500 mb-2">⚠️ 组件加载失败</div>
    <div className="text-sm text-gray-600 mb-3">{error.message}</div>
    <button onClick={resetErrorBoundary} className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600">
      重试
    </button>
  </div>
)

// 懒加载包装器（带错误边界）
const LazyWrapper = ({children}: {children: React.ReactNode}) => (
  <Suspense fallback={<LoadingSpinner />}>{children}</Suspense>
)

// 预加载函数
export const preloadRoutes = () => {
  // 预加载所有路由组件
  import('@/entrypoints/popup/Dimension')
  import('@/entrypoints/popup/Design')
  import('@/entrypoints/popup/SettingDialog')
}
// 懒加载Design组件
const Design = lazy(() => import('@/entrypoints/popup/Design'))

// 定义路由配置
export const routes = [
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Navigate to="/code/dimension" replace />,
      },
      {
        path: 'code',
        children: [
          {
            index: true,
            element: (
              <LazyWrapper>
                <Dimension />
              </LazyWrapper>
            ),
          },
          {
            path: 'dimension',
            element: (
              <LazyWrapper>
                <Dimension />
              </LazyWrapper>
            ),
          },
          {
            path: 'images',
            element: (
              <LazyWrapper>
                <CutDiagram />
              </LazyWrapper>
            ),
          },
          {
            path: 'plugins',
            element: <PluginsSection />,
          },
        ],
      },
      {
        path: 'design',
        element: (
          <LazyWrapper>
            <Design />
          </LazyWrapper>
        ),
      },
    ],
  },
]

// 创建Hash路由器
export const router = createMemoryRouter(routes)

// 路由提供者组件
export const RouterWrapper = () => {
  return <RouterProvider router={router} />
}

// 路由导航工具函数
export const navigateTo = (path: string) => {
  window.location.hash = path
}

// 获取当前路由
export const getCurrentRoute = () => {
  return window.location.hash.slice(1) || '/'
}

// 路由常量
export const ROUTES = {
  HOME: '/',
  CODE: '/code',
  CODE_DIMENSION: '/code/dimension',
  CODE_IMAGES: '/code/images',
  CODE_PLUGINS: '/code/plugins',
  DESIGN: '/design',
} as const
