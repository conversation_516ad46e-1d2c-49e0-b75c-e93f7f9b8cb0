// 测试 multipart/form-data 功能
const FormData = require('form-data');
const fs = require('fs');

console.log('测试 multipart/form-data 功能...');

// 创建一个简单的测试图片文件
const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
const buffer = Buffer.from(testImageBase64, 'base64');

// 创建 FormData
const formData = new FormData();
formData.append('token', 'test-token');
formData.append('file', buffer, {
  filename: 'test.png',
  contentType: 'image/png'
});

// 发送请求
fetch('http://localhost:3000/tinify/compress', {
  method: 'POST',
  body: formData,
  headers: formData.getHeaders()
})
.then(response => {
  console.log('状态码:', response.status);
  console.log('Content-Type:', response.headers.get('content-type'));
  
  if (response.headers.get('content-type')?.includes('application/json')) {
    return response.json();
  } else {
    return response.text();
  }
})
.then(data => {
  console.log('响应:', data);
})
.catch(error => {
  console.error('错误:', error.message);
});
